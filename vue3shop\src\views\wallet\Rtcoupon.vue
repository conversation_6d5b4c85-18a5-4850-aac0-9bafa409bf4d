<template>
  <div class="coupon-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <el-button 
          type="primary" 
          :icon="ArrowLeft" 
          circle 
          @click="goBack"
          class="back-btn"
        />
        <h1 class="page-title">我的优惠券</h1>
        <div class="coupon-count">共 {{ filteredCoupons.length }} 张</div>
      </div>
    </div>

    <!-- 筛选标签 -->
    <div class="filter-tabs">
      <div 
        v-for="tab in filterTabs" 
        :key="tab.key"
        :class="['filter-tab', { active: activeFilter === tab.key }]"
        @click="setActiveFilter(tab.key)"
      >
        {{ tab.label }}
        <span class="tab-count">({{ getCountByStatus(tab.key) }})</span>
      </div>
    </div>

    <!-- 优惠券列表 -->
    <div class="coupon-list">
      <div v-if="loading" class="loading-container">
        <el-icon class="loading-icon"><Loading /></el-icon>
        <span>加载中...</span>
      </div>
      
      <div v-else-if="filteredCoupons.length === 0" class="empty-container">
        <el-icon class="empty-icon"><DocumentRemove /></el-icon>
        <p class="empty-text">暂无{{ getFilterLabel() }}优惠券</p>
      </div>
      
      <div v-else class="coupon-grid">
        <div 
          v-for="coupon in filteredCoupons" 
          :key="coupon.id"
          :class="['coupon-card', coupon.status]"
        >
          <!-- 优惠券左侧金额区域 -->
          <div class="coupon-left">
            <div class="coupon-amount">
              <span class="currency">￥</span>
              <span class="amount">{{ coupon.amount }}</span>
            </div>
            <div class="coupon-condition">{{ coupon.condition }}</div>
          </div>
          
          <!-- 优惠券右侧信息区域 -->
          <div class="coupon-right">
            <div class="coupon-info">
              <h3 class="coupon-title">{{ coupon.title }}</h3>
              <p class="coupon-desc">{{ coupon.description }}</p>
              <div class="coupon-time">
                <el-icon><Clock /></el-icon>
                <span>{{ coupon.validTime }}</span>
              </div>
            </div>
            
            <!-- 优惠券状态/操作按钮 -->
            <div class="coupon-action">
              <el-button 
                v-if="coupon.status === 'available'"
                type="primary"
                size="small"
                @click="useCoupon(coupon)"
              >
                立即使用
              </el-button>
              <span v-else-if="coupon.status === 'used'" class="status-text used">已使用</span>
              <span v-else-if="coupon.status === 'expired'" class="status-text expired">已过期</span>
            </div>
          </div>
          
          <!-- 优惠券类型标签 -->
          <div class="coupon-tag">{{ coupon.type }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { ArrowLeft, Clock, Loading, DocumentRemove } from '@element-plus/icons-vue'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const activeFilter = ref('all')
const coupons = ref([])

// 筛选标签配置
const filterTabs = [
  { key: 'all', label: '全部' },
  { key: 'available', label: '可使用' },
  { key: 'used', label: '已使用' },
  { key: 'expired', label: '已过期' }
]

// 模拟优惠券数据
const mockCoupons = [
  {
    id: 1,
    title: '新用户专享优惠券',
    description: '适用于全场商品，新用户首次购买专享',
    amount: 50,
    condition: '满200元可用',
    type: '通用券',
    status: 'available',
    validTime: '2024-12-31前有效',
    createTime: '2024-01-15'
  },
  {
    id: 2,
    title: '电脑数码专用券',
    description: '仅限电脑、手机等数码产品使用',
    amount: 100,
    condition: '满500元可用',
    type: '品类券',
    status: 'available',
    validTime: '2024-11-30前有效',
    createTime: '2024-02-01'
  },
  {
    id: 3,
    title: '生日特惠券',
    description: '生日月专享优惠，感谢您的支持',
    amount: 30,
    condition: '满150元可用',
    type: '生日券',
    status: 'used',
    validTime: '2024-10-31前有效',
    createTime: '2024-03-10'
  },
  {
    id: 4,
    title: '周年庆优惠券',
    description: '平台周年庆活动专用优惠券',
    amount: 200,
    condition: '满1000元可用',
    type: '活动券',
    status: 'expired',
    validTime: '2024-09-30前有效',
    createTime: '2024-01-01'
  },
  {
    id: 5,
    title: 'VIP专享券',
    description: 'VIP会员专享，尊享更多优惠',
    amount: 80,
    condition: '满300元可用',
    type: 'VIP券',
    status: 'available',
    validTime: '2024-12-15前有效',
    createTime: '2024-04-20'
  },
  {
    id: 6,
    title: '满减优惠券',
    description: '购物满额立减，多买多省',
    amount: 25,
    condition: '满100元可用',
    type: '满减券',
    status: 'available',
    validTime: '2024-11-15前有效',
    createTime: '2024-05-01'
  }
]

// 计算属性：根据筛选条件过滤优惠券
const filteredCoupons = computed(() => {
  if (activeFilter.value === 'all') {
    return coupons.value
  }
  return coupons.value.filter(coupon => coupon.status === activeFilter.value)
})

// 获取各状态优惠券数量
const getCountByStatus = (status) => {
  if (status === 'all') {
    return coupons.value.length
  }
  return coupons.value.filter(coupon => coupon.status === status).length
}

// 获取当前筛选条件的标签
const getFilterLabel = () => {
  const tab = filterTabs.find(tab => tab.key === activeFilter.value)
  return tab ? tab.label : ''
}

// 设置筛选条件
const setActiveFilter = (filter) => {
  activeFilter.value = filter
}

// 使用优惠券
const useCoupon = (coupon) => {
  ElMessage.success(`已选择优惠券：${coupon.title}`)
  // 这里可以添加跳转到购物车或商品页面的逻辑
  router.push('/cart')
}

// 返回上一页
const goBack = () => {
  router.back()
}

// 加载优惠券数据
const loadCoupons = async () => {
  loading.value = true
  try {
    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 800))
    coupons.value = mockCoupons
  } catch (error) {
    console.error('加载优惠券失败:', error)
    ElMessage.error('加载优惠券失败')
  } finally {
    loading.value = false
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadCoupons()
})
</script>

<style scoped>
.coupon-page {
  min-height: 100vh;
  background: #f5f7fa;
  padding-bottom: 20px;
}

.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px;
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.header-content {
  display: flex;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
  gap: 16px;
}

.back-btn {
  background: rgba(255,255,255,0.2);
  border: none;
  color: white;
}

.back-btn:hover {
  background: rgba(255,255,255,0.3);
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  margin: 0;
  flex: 1;
}

.coupon-count {
  font-size: 14px;
  opacity: 0.9;
}

.filter-tabs {
  display: flex;
  background: white;
  margin: 0 20px 20px 20px;
  border-radius: 12px;
  padding: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.06);
  gap: 4px;
}

.filter-tab {
  flex: 1;
  text-align: center;
  padding: 12px 16px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
  color: #666;
}

.filter-tab:hover {
  background: #f0f2f5;
  color: #333;
}

.filter-tab.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.tab-count {
  font-size: 12px;
  margin-left: 4px;
  opacity: 0.8;
}

.coupon-list {
  padding: 0 20px;
}

.loading-container, .empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: #999;
}

.loading-icon, .empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.empty-text {
  font-size: 16px;
  margin: 0;
}

.coupon-grid {
  display: grid;
  gap: 16px;
  max-width: 1200px;
  margin: 0 auto;
}

.coupon-card {
  background: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0,0,0,0.08);
  display: flex;
  position: relative;
  transition: all 0.3s ease;
}

.coupon-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0,0,0,0.12);
}

.coupon-card.expired {
  opacity: 0.6;
  filter: grayscale(0.3);
}

.coupon-left {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  color: white;
  padding: 24px 20px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-width: 140px;
  position: relative;
}

.coupon-left::after {
  content: '';
  position: absolute;
  right: -8px;
  top: 50%;
  transform: translateY(-50%);
  width: 16px;
  height: 16px;
  background: #f5f7fa;
  border-radius: 50%;
}

.coupon-amount {
  display: flex;
  align-items: baseline;
  margin-bottom: 8px;
}

.currency {
  font-size: 16px;
  margin-right: 2px;
}

.amount {
  font-size: 32px;
  font-weight: bold;
}

.coupon-condition {
  font-size: 12px;
  opacity: 0.9;
  text-align: center;
}

.coupon-right {
  flex: 1;
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.coupon-info {
  flex: 1;
}

.coupon-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0 0 8px 0;
}

.coupon-desc {
  font-size: 14px;
  color: #666;
  margin: 0 0 12px 0;
  line-height: 1.4;
}

.coupon-time {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #999;
  gap: 4px;
}

.coupon-action {
  margin-left: 20px;
}

.status-text {
  font-size: 14px;
  font-weight: 500;
  padding: 6px 12px;
  border-radius: 6px;
}

.status-text.used {
  background: #e8f5e8;
  color: #52c41a;
}

.status-text.expired {
  background: #fff2f0;
  color: #ff4d4f;
}

.coupon-tag {
  position: absolute;
  top: 12px;
  right: 12px;
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 4px;
  font-weight: 500;
}

@media (max-width: 768px) {
  .coupon-page {
    padding-bottom: 10px;
  }

  .page-header {
    padding: 16px;
  }

  .page-title {
    font-size: 20px;
  }

  .filter-tabs {
    margin: 0 16px 16px 16px;
    padding: 6px;
  }

  .filter-tab {
    padding: 10px 12px;
    font-size: 14px;
  }

  .coupon-list {
    padding: 0 16px;
  }

  .coupon-card {
    flex-direction: column;
  }

  .coupon-left {
    min-width: auto;
    padding: 16px;
    flex-direction: row;
    justify-content: space-between;
  }

  .coupon-left::after {
    display: none;
  }

  .coupon-right {
    padding: 16px;
    flex-direction: column;
    align-items: flex-start;
  }

  .coupon-action {
    margin-left: 0;
    margin-top: 12px;
    align-self: flex-end;
  }
}
</style>
